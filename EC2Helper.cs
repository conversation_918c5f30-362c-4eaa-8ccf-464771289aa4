using Amazon;
using Amazon.EC2;
using Amazon.EC2.Model;
using Amazon.Runtime;
using log4net;
using log4net.Config;
using System;
using System.Collections.Generic;
using System.Threading;

namespace PAws
{
    public class EC2Helper
    {
        private readonly static ILog Enginelog;

        static EC2Helper()
        {
            EC2Helper.Enginelog = LogManager.GetLogger("BcmsEngineLog");
            XmlConfigurator.Configure();
        }

        public EC2Helper()
        {
            XmlConfigurator.Configure();
        }

        public static bool CheckEC2InstanceState(string awsKey, string awsSecretKey, string awsId, string awsRegion, string instanceState)
        {
            bool flag;
            try
            {
                flag = (EC2Helper.GetEC2InstanceState(awsKey, awsSecretKey, awsId, awsRegion).ToLower() != instanceState.ToLower() ? false : true);
            }
            catch (AmazonEC2Exception amazonEC2Exception1)
            {
                AmazonEC2Exception amazonEC2Exception = amazonEC2Exception1;
                EC2Helper.Enginelog.Info(string.Concat("Exception CheckEC2InstanceState:", amazonEC2Exception.Message));
                throw amazonEC2Exception;
            }
            return flag;
        }

        public static EC2InstanceInfo GetEC2InstanceInfo(string awsKey, string awsSecretKey, string awsId, string awsRegion)
        {
            EC2InstanceInfo eC2InstanceInfo;
            try
            {
                EC2InstanceInfo publicIpAddress = new EC2InstanceInfo();
                IAmazonEC2 amazonEC2 = AWSClientFactory.CreateAmazonEC2Client(awsKey, awsSecretKey, EC2Helper.GetRegion(awsRegion));
                DescribeInstancesRequest describeInstancesRequest = new DescribeInstancesRequest();
                List<Reservation> reservations = amazonEC2.DescribeInstances(describeInstancesRequest).DescribeInstancesResult.Reservations;
                amazonEC2.DescribeInstances(describeInstancesRequest);
                foreach (Reservation reservation in reservations)
                {
                    for (int i = 0; i < reservation.Instances.Count; i++)
                    {
                        if (reservation.Instances[i].InstanceId == awsId)
                        {
                            publicIpAddress.InstanceId = awsId;
                            publicIpAddress.InstancePublicIP = reservation.Instances[i].PublicIpAddress;
                            publicIpAddress.InstanceState = reservation.Instances[i].State.Name;
                            publicIpAddress.InstanceType = reservation.Instances[i].InstanceType.ToString();
                            publicIpAddress.InstancePlatform = reservation.Instances[i].Platform.ToString();
                            publicIpAddress.InstancePrivateIP = reservation.Instances[i].NetworkInterfaces[0].PrivateIpAddress;
                            DateTime launchTime = reservation.Instances[i].LaunchTime;
                            publicIpAddress.InstanceLaunchTime = launchTime.ToShortDateString();
                        }
                    }
                }
                eC2InstanceInfo = publicIpAddress;
            }
            catch (AmazonEC2Exception amazonEC2Exception1)
            {
                AmazonEC2Exception amazonEC2Exception = amazonEC2Exception1;
                EC2Helper.Enginelog.Info(string.Concat("Exception:", amazonEC2Exception.Message));
                eC2InstanceInfo = null;
            }
            return eC2InstanceInfo;
        }

        public static string GetEC2InstanceState(string awsKey, string awsSecretKey, string awsId, string awsRegion, string instanceType)
        {
            string str;
            try
            {
                IAmazonEC2 amazonEC2 = AWSClientFactory.CreateAmazonEC2Client(awsKey, awsSecretKey, EC2Helper.GetRegion(awsRegion));
                DescribeInstanceAttributeRequest describeInstanceAttributeRequest = new DescribeInstanceAttributeRequest()
                {
                    InstanceId = awsId,
                    Attribute = InstanceAttributeName.InstanceType
                };
                str = amazonEC2.DescribeInstanceAttribute(describeInstanceAttributeRequest).InstanceAttribute.InstanceType;
            }
            catch (AmazonEC2Exception amazonEC2Exception1)
            {
                AmazonEC2Exception amazonEC2Exception = amazonEC2Exception1;
                EC2Helper.Enginelog.Info(string.Concat("Exception GetEC2InstanceState:", amazonEC2Exception.Message));
                str = "Unknown";
            }
            return str;
        }

        public static string GetEC2InstanceState(string awsKey, string awsSecretKey, string awsId, string awsRegion)
        {
            string name;
            try
            {
                IAmazonEC2 amazonEC2 = AWSClientFactory.CreateAmazonEC2Client(awsKey, awsSecretKey, EC2Helper.GetRegion(awsRegion));
                foreach (Reservation reservation in amazonEC2.DescribeInstances(new DescribeInstancesRequest()).DescribeInstancesResult.Reservations)
                {
                    int num = 0;
                    while (num < reservation.Instances.Count)
                    {
                        if (reservation.Instances[num].InstanceId != awsId)
                        {
                            num++;
                        }
                        else
                        {
                            name = reservation.Instances[num].State.Name;
                            return name;
                        }
                    }
                }
                name = "Unknown";
            }
            catch (Exception exception1)
            {
                Exception exception = exception1;
                EC2Helper.Enginelog.Info(string.Concat("Exception GetEC2InstanceState:", exception.Message));
                name = "Unknown";
            }
            return name;
        }

        public static string GetEC2InstanceStatusCheck(string awsKey, string awsSecretKey, string awsId, string awsRegion)
        {
            string str;
            try
            {
                IAmazonEC2 amazonEC2 = AWSClientFactory.CreateAmazonEC2Client(awsKey, awsSecretKey, EC2Helper.GetRegion(awsRegion));
                DescribeInstanceStatusRequest describeInstanceStatusRequest = new DescribeInstanceStatusRequest()
                {
                    InstanceIds = new List<string>()
                    {
                        awsId
                    }
                };
                DescribeInstanceStatusResult describeInstanceStatusResult = amazonEC2.DescribeInstanceStatus(describeInstanceStatusRequest).DescribeInstanceStatusResult;
                int num = 0;
                while (num < describeInstanceStatusResult.InstanceStatuses.Count)
                {
                    if (describeInstanceStatusResult.InstanceStatuses[num].InstanceId != awsId)
                    {
                        num++;
                    }
                    else
                    {
                        str = describeInstanceStatusResult.InstanceStatuses[num].Status.Status.ToString();
                        return str;
                    }
                }
                str = "Unknown";
            }
            catch (Exception exception1)
            {
                Exception exception = exception1;
                EC2Helper.Enginelog.Info(string.Concat("Exception GetEC2InstanceStatusCheck:", exception.Message));
                str = "Unknown";
            }
            return str;
        }

        public static string GetEC2InstanceType(string awsKey, string awsSecretKey, string awsId, string awsRegion)
        {
            string lower;
            try
            {
                IAmazonEC2 amazonEC2 = AWSClientFactory.CreateAmazonEC2Client(awsKey, awsSecretKey, EC2Helper.GetRegion(awsRegion));
                DescribeInstancesRequest describeInstancesRequest = new DescribeInstancesRequest();
                List<Reservation> reservations = amazonEC2.DescribeInstances(describeInstancesRequest).DescribeInstancesResult.Reservations;
                DescribeInstancesResult describeInstancesResult = amazonEC2.DescribeInstances(describeInstancesRequest).DescribeInstancesResult;
                foreach (Reservation reservation in reservations)
                {
                    int num = 0;
                    while (num < reservation.Instances.Count)
                    {
                        if (reservation.Instances[num].InstanceId != awsId)
                        {
                            num++;
                        }
                        else
                        {
                            lower = reservation.Instances[num].InstanceType.ToString().ToLower();
                            return lower;
                        }
                    }
                }
                lower = "Unknown";
            }
            catch (Exception exception1)
            {
                Exception exception = exception1;
                EC2Helper.Enginelog.Info(string.Concat("Exception GetEC2InstanceType:", exception.Message));
                lower = "Unknown";
            }
            return lower;
        }

        public static RegionEndpoint GetRegion(string awsRegion)
        {
            string lower = awsRegion.ToLower();
            string str = lower;
            if (lower != null)
            {
                switch (str)
                {
                    case "us-east-1":
                        {
                            return RegionEndpoint.USEast1;
                        }
                    case "us-west-1":
                        {
                            return RegionEndpoint.USWest1;
                        }
                    case "us-west-2":
                        {
                            return RegionEndpoint.USWest2;
                        }
                    case "sa-east-1":
                        {
                            return RegionEndpoint.SAEast1;
                        }
                    case "eu-west-1":
                        {
                            return RegionEndpoint.EUWest1;
                        }
                    case "eu-central-1":
                        {
                            return RegionEndpoint.EUCentral1;
                        }
                    case "ap-southeast-2":
                        {
                            return RegionEndpoint.APSoutheast2;
                        }
                    case "ap-southeast-1":
                        {
                            return RegionEndpoint.APSoutheast1;
                        }
                    case "ap-northeast-1":
                        {
                            return RegionEndpoint.APNortheast1;
                        }
                    case "cnn-north-1":
                        {
                            return RegionEndpoint.CNNorth1;
                        }
                    case "us-govcloudwest-1":
                        {
                            return RegionEndpoint.USGovCloudWest1;
                        }
                }
            }
            return RegionEndpoint.USWest2;
        }

        public static bool IsEC2InstanceRunning(string awsKey, string awsSecretKey, string awsId, string awsRegion)
        {
            bool flag;
            try
            {
                flag = (!EC2Helper.CheckEC2InstanceState(awsKey, awsSecretKey, awsId, awsRegion, "running") ? false : true);
            }
            catch (AmazonEC2Exception amazonEC2Exception1)
            {
                AmazonEC2Exception amazonEC2Exception = amazonEC2Exception1;
                EC2Helper.Enginelog.Info(string.Concat("Exception IsEC2InstanceRunning:", amazonEC2Exception.Message));
                throw amazonEC2Exception;
            }
            return flag;
        }

        public static bool IsEC2InstanceStopped(string awsKey, string awsSecretKey, string awsId, string awsRegion)
        {
            bool flag;
            try
            {
                flag = (!EC2Helper.CheckEC2InstanceState(awsKey, awsSecretKey, awsId, awsRegion, "stopped") ? false : true);
            }
            catch (AmazonEC2Exception amazonEC2Exception1)
            {
                AmazonEC2Exception amazonEC2Exception = amazonEC2Exception1;
                EC2Helper.Enginelog.Info(string.Concat("Exception IsEC2InstanceStopped:", amazonEC2Exception.Message));
                throw amazonEC2Exception;
            }
            return flag;
        }

        public static bool ModifyEC2InstanceType(string awsKey, string awsSecretKey, string awsId, string awsRegion, string instanceType)
        {
            bool flag;
            try
            {
                IAmazonEC2 amazonEC2 = AWSClientFactory.CreateAmazonEC2Client(awsKey, awsSecretKey, EC2Helper.GetRegion(awsRegion));
                ModifyInstanceAttributeRequest modifyInstanceAttributeRequest = new ModifyInstanceAttributeRequest()
                {
                    InstanceId = awsId,
                    InstanceType = instanceType
                };
                amazonEC2.ModifyInstanceAttribute(modifyInstanceAttributeRequest);
                flag = (EC2Helper.GetEC2InstanceType(awsKey, awsSecretKey, awsId, awsRegion) != instanceType ? false : true);
            }
            catch (AmazonEC2Exception amazonEC2Exception1)
            {
                AmazonEC2Exception amazonEC2Exception = amazonEC2Exception1;
                EC2Helper.Enginelog.Info(string.Concat("Exception ModifyEC2InstanceType:", amazonEC2Exception.Message));
                throw amazonEC2Exception;
            }
            return flag;
        }

        public static bool StartEC2Instance(string awsKey, string awsSecretKey, string awsId, string awsRegion)
        {
            bool flag;
            try
            {
                IAmazonEC2 amazonEC2 = AWSClientFactory.CreateAmazonEC2Client(awsKey, awsSecretKey, EC2Helper.GetRegion(awsRegion));
                DescribeInstancesRequest describeInstancesRequest = new DescribeInstancesRequest();
                StartInstancesRequest startInstancesRequest = new StartInstancesRequest();
                startInstancesRequest.InstanceIds.Add(awsId);
                amazonEC2.StartInstances(startInstancesRequest);
                EC2Helper.WaitEC2InstanceState(awsKey, awsSecretKey, awsId, awsRegion, "running");
                flag = true;
            }
            catch (AmazonEC2Exception amazonEC2Exception1)
            {
                AmazonEC2Exception amazonEC2Exception = amazonEC2Exception1;
                EC2Helper.Enginelog.Info(string.Concat("Exception StartInstance:", amazonEC2Exception.Message));
                throw amazonEC2Exception;
            }
            return flag;
        }

        public static bool StopEC2Instance(string awsKey, string awsSecretKey, string awsId, string awsRegion)
        {
            bool flag;
            try
            {
                IAmazonEC2 amazonEC2 = AWSClientFactory.CreateAmazonEC2Client(awsKey, awsSecretKey, EC2Helper.GetRegion(awsRegion));
                DescribeInstancesRequest describeInstancesRequest = new DescribeInstancesRequest();
                StopInstancesRequest stopInstancesRequest = new StopInstancesRequest();
                stopInstancesRequest.InstanceIds.Add(awsId);
                amazonEC2.StopInstances(stopInstancesRequest);
                EC2Helper.WaitEC2InstanceState(awsKey, awsSecretKey, awsId, awsRegion, "stopped");
                Thread.Sleep(30000);
                flag = true;
            }
            catch (AmazonEC2Exception amazonEC2Exception1)
            {
                AmazonEC2Exception amazonEC2Exception = amazonEC2Exception1;
                EC2Helper.Enginelog.Info(string.Concat("Exception StopInstance:", amazonEC2Exception.Message));
                throw amazonEC2Exception;
            }
            return flag;
        }

        public static bool WaitEC2InstanceState(string awsKey, string awsSecretKey, string awsId, string awsRegion, string instanceState)
        {
            bool flag;
            try
            {
                string eC2InstanceState = EC2Helper.GetEC2InstanceState(awsKey, awsSecretKey, awsId, awsRegion);
                int num = 0;
                bool flag1 = false;
                if (eC2InstanceState != instanceState)
                {
                    while (true)
                    {
                        num++;
                        Thread.Sleep(5000);
                        if (EC2Helper.GetEC2InstanceState(awsKey, awsSecretKey, awsId, awsRegion).ToLower() == instanceState.ToLower())
                        {
                            flag1 = true;
                            break;
                        }
                        else if (num > 100)
                        {
                            flag1 = false;
                            break;
                        }
                    }
                    flag = flag1;
                }
                else
                {
                    flag = true;
                }
            }
            catch (AmazonEC2Exception amazonEC2Exception1)
            {
                AmazonEC2Exception amazonEC2Exception = amazonEC2Exception1;
                EC2Helper.Enginelog.Info(string.Concat("Exception WaitEC2InstanceState:", amazonEC2Exception.Message));
                flag = false;
            }
            return flag;
        }
    }
}